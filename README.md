# Sailand Technology LTD Website

A complete, responsive e-commerce-style website for Sailand Technology LTD, specializing in enterprise-grade and consumer IT products.

## 🚀 Features

- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Brand-Specific Styling**: Product cards adapt colors based on manufacturer (HP, Dell, Lenovo, Logitech, SanDisk, Lexar)
- **WhatsApp Integration**: Easy ordering through WhatsApp with pre-filled messages
- **Product Filtering**: Search and filter products by category
- **Professional Layout**: Clean, modern design with red (#D32F2F) and white color scheme
- **SEO Ready**: Semantic HTML5 structure with proper meta tags

## 📁 File Structure

```
sailand website/
├── index.html          # Home page with hero banner and category highlights
├── products.html       # Products page with filtering and search
├── about.html          # About Us page with company information
├── contact.html        # Contact page with form and WhatsApp integration
├── css/
│   └── style.css       # Main stylesheet with responsive design
├── js/
│   └── script.js       # JavaScript for filtering, search, and WhatsApp integration
├── images/             # Placeholder for product images
└── README.md           # This file
```

## 🎨 Brand Colors

- **Primary Red**: #D32F2F (headers, buttons, highlights)
- **White**: #FFFFFF (backgrounds, text on red)
- **Brand-Specific Accents**:
  - HP: Blue (#0076CE)
  - Dell: Blue (#007DB8)
  - Lenovo: Black & Red
  - Logitech: Teal (#00B8A9)
  - SanDisk: Red (#D32F2F)
  - Lexar: Dark Gray (#555)
  - Generic: Light Gray (#ccc)

## 📱 WhatsApp Integration

The website includes WhatsApp integration for easy ordering:

1. **Update Phone Number**: Edit the `WHATSAPP_NUMBER` variable in `js/script.js`
2. **Product Orders**: Each product has a "Order on WhatsApp" button
3. **Contact Form**: Submits via WhatsApp with pre-filled message
4. **Quick Actions**: Various WhatsApp shortcuts on contact page

```javascript
// In js/script.js, line 85
const WHATSAPP_NUMBER = "1234567890"; // Replace with actual number
```

## 🛠️ Customization Guide

### Adding Products

Edit the `products` array in `js/script.js`:

```javascript
{
    id: 13,
    name: "Your Product Name",
    description: "Product description",
    price: "Contact for Price",
    category: "laptops", // or desktops, accessories, etc.
    brand: "hp", // affects card styling
    image: "https://via.placeholder.com/300x200?text=Product+Image"
}
```

### Updating Images

1. Replace placeholder URLs in `js/script.js` with actual product images
2. Add images to the `images/` folder
3. Update image paths in the product data

### Contact Information

Update contact details in:
- Footer sections (all HTML files)
- Contact page (`contact.html`)
- WhatsApp numbers in JavaScript

### Company Information

- Edit company description in `about.html`
- Update hero section slogans in `index.html`
- Modify footer content across all pages

## 📱 Responsive Breakpoints

- **Desktop**: 1200px and above
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px
- **Small Mobile**: Below 480px

## 🔧 Technical Features

### CSS Features
- CSS Grid for responsive layouts
- Flexbox for component alignment
- CSS transitions and hover effects
- Sticky navigation
- Brand-specific color theming

### JavaScript Features
- Product filtering and search
- Mobile menu toggle
- WhatsApp link generation
- Contact form handling
- URL parameter support for category filtering

### HTML Features
- Semantic HTML5 structure
- Proper meta tags for SEO
- Accessible form labels
- Structured data ready

## 🚀 Getting Started

1. **Open the Website**: Double-click `index.html` or open in any web browser
2. **Update Phone Number**: Edit WhatsApp number in `js/script.js`
3. **Add Real Images**: Replace placeholder images with actual product photos
4. **Customize Content**: Update company information, product details, and contact info
5. **Test Functionality**: Check all links, forms, and responsive design

## 📞 WhatsApp Setup

To set up WhatsApp integration:

1. Get your WhatsApp Business number
2. Format it as international number (e.g., "1234567890" for ******-567-8900)
3. Update the `WHATSAPP_NUMBER` constant in `js/script.js`
4. Test the WhatsApp links to ensure they work correctly

## 🎯 SEO Optimization

The website is SEO-ready with:
- Proper meta descriptions and keywords
- Semantic HTML structure
- Alt tags for images (add when replacing placeholders)
- Clean URL structure
- Mobile-friendly design

## 📝 Notes

- All placeholder images use `via.placeholder.com` service
- Contact information is placeholder data - update with real details
- Product data is sample data - replace with actual inventory
- WhatsApp number needs to be updated for functionality
- Website is ready to run locally or deploy to any web server

## 🔄 Future Enhancements

Consider adding:
- Shopping cart functionality
- User accounts and login
- Payment gateway integration
- Inventory management
- Order tracking system
- Live chat support
- Product reviews and ratings

---

**Sailand Technology LTD** - Professional IT Solutions

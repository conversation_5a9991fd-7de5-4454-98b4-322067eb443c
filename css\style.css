/* Sailand Technology LTD - Main Stylesheet */
/* Primary Colors: Red (#D32F2F) and White (#FFFFFF) */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #FFFFFF;
}

/* Header and Navigation */
header {
    background-color: #D32F2F;
    color: white;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #ffcccb;
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #D32F2F 0%, #B71C1C 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    background-color: white;
    color: #D32F2F;
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.cta-button:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Product Grid */
.products-section {
    padding: 4rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: #D32F2F;
    margin-bottom: 3rem;
}

/* Search and Filter Bar */
.filter-bar {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    align-items: center;
}

.search-box {
    flex: 1;
    min-width: 250px;
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.search-box:focus {
    outline: none;
    border-color: #D32F2F;
}

.category-filter {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    background-color: white;
    cursor: pointer;
}

.category-filter:focus {
    outline: none;
    border-color: #D32F2F;
}

/* Product Grid Layout */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Product Cards with Brand-Specific Styling */
.product-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid #ddd; /* Default border */
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Brand-specific card styling */
.product-card.hp { border-top-color: #0076CE; }
.product-card.dell { border-top-color: #007DB8; }
.product-card.lenovo { border-top-color: #D32F2F; }
.product-card.logitech { border-top-color: #00B8A9; }
.product-card.sandisk { border-top-color: #D32F2F; }
.product-card.lexar { border-top-color: #555; }
.product-card.generic { border-top-color: #ccc; }

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.product-description {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.product-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: #D32F2F;
    margin-bottom: 1rem;
}

.whatsapp-btn {
    background-color: #25D366;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    width: 100%;
    text-align: center;
    transition: background-color 0.3s ease;
}

.whatsapp-btn:hover {
    background-color: #128C7E;
}

/* About and Contact Pages */
.page-content {
    padding: 4rem 0;
    max-width: 800px;
    margin: 0 auto;
}

.page-content h2 {
    color: #D32F2F;
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.page-content p {
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

/* Contact Form */
.contact-form {
    background: #f9f9f9;
    padding: 2rem;
    border-radius: 10px;
    margin-top: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #D32F2F;
}

.form-group textarea {
    height: 120px;
    resize: vertical;
}

/* Footer */
footer {
    background-color: #333;
    color: white;
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: #D32F2F;
    margin-bottom: 1rem;
}

.footer-section p,
.footer-section a {
    color: #ccc;
    text-decoration: none;
    line-height: 1.8;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #D32F2F;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .nav-links.active {
        display: flex;
    }

    .menu-toggle {
        display: block;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    .filter-bar {
        flex-direction: column;
    }

    .search-box {
        min-width: 100%;
    }

    .product-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 2rem 0;
    }

    .products-section {
        padding: 2rem 0;
    }

    .page-content {
        padding: 2rem 0;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-2 { margin-top: 2rem; }
.hidden { display: none; }

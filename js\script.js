// Sailand Technology LTD - Main JavaScript File

// Sample product data - Replace with actual product data
const products = [
    // Laptops
    {
        id: 1,
        name: "HP EliteBook 840 G9",
        description: "Professional business laptop with Intel Core i7 processor",
        price: "Contact for Price",
        category: "laptops",
        brand: "hp",
        image: "https://via.placeholder.com/300x200?text=HP+EliteBook+840"
    },
    {
        id: 2,
        name: "Dell Latitude 7420",
        description: "Premium business laptop with excellent build quality",
        price: "Contact for Price",
        category: "laptops",
        brand: "dell",
        image: "https://via.placeholder.com/300x200?text=Dell+Latitude+7420"
    },
    {
        id: 3,
        name: "Lenovo ThinkPad X1 Carbon",
        description: "Ultra-lightweight business laptop with carbon fiber construction",
        price: "Contact for Price",
        category: "laptops",
        brand: "lenovo",
        image: "https://via.placeholder.com/300x200?text=Lenovo+ThinkPad+X1"
    },
    
    // Desktops
    {
        id: 4,
        name: "HP EliteDesk 800 G9",
        description: "Compact desktop PC for business environments",
        price: "Contact for Price",
        category: "desktops",
        brand: "hp",
        image: "https://via.placeholder.com/300x200?text=HP+EliteDesk+800"
    },
    {
        id: 5,
        name: "Dell OptiPlex 7090",
        description: "Reliable desktop solution for enterprise use",
        price: "Contact for Price",
        category: "desktops",
        brand: "dell",
        image: "https://via.placeholder.com/300x200?text=Dell+OptiPlex+7090"
    },
    
    // Accessories
    {
        id: 6,
        name: "Logitech MX Master 3S",
        description: "Advanced wireless mouse for professionals",
        price: "Contact for Price",
        category: "accessories",
        brand: "logitech",
        image: "https://via.placeholder.com/300x200?text=Logitech+MX+Master+3S"
    },
    {
        id: 7,
        name: "HP Wireless Keyboard",
        description: "Ergonomic wireless keyboard for office use",
        price: "Contact for Price",
        category: "accessories",
        brand: "hp",
        image: "https://via.placeholder.com/300x200?text=HP+Wireless+Keyboard"
    },
    
    // Flash Drives
    {
        id: 8,
        name: "SanDisk Ultra USB 3.0 64GB",
        description: "High-speed USB flash drive for data storage",
        price: "Contact for Price",
        category: "flash-drives",
        brand: "sandisk",
        image: "https://via.placeholder.com/300x200?text=SanDisk+Ultra+64GB"
    },
    
    // RAM
    {
        id: 9,
        name: "Lexar DDR4 16GB 3200MHz",
        description: "High-performance RAM module for desktop computers",
        price: "Contact for Price",
        category: "ram",
        brand: "lexar",
        image: "https://via.placeholder.com/300x200?text=Lexar+DDR4+16GB"
    },
    
    // Monitors
    {
        id: 10,
        name: "HP 24\" Full HD Monitor",
        description: "Professional 24-inch monitor with IPS panel",
        price: "Contact for Price",
        category: "monitors",
        brand: "hp",
        image: "https://via.placeholder.com/300x200?text=HP+24+Monitor"
    },
    
    // CCTV
    {
        id: 11,
        name: "Security Camera System",
        description: "Complete CCTV surveillance system for business security",
        price: "Contact for Price",
        category: "cctv",
        brand: "generic",
        image: "https://via.placeholder.com/300x200?text=CCTV+System"
    },
    
    // Servers
    {
        id: 12,
        name: "HP ProLiant DL380 Gen10",
        description: "Enterprise-grade rack server for data centers",
        price: "Contact for Price",
        category: "servers",
        brand: "hp",
        image: "https://via.placeholder.com/300x200?text=HP+ProLiant+Server"
    }
];

// WhatsApp phone number - Replace with actual number
const WHATSAPP_NUMBER = "1234567890"; // Replace with actual WhatsApp number

// DOM Elements
let productGrid;
let searchBox;
let categoryFilter;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Get DOM elements
    productGrid = document.getElementById('product-grid');
    searchBox = document.getElementById('search-box');
    categoryFilter = document.getElementById('category-filter');
    
    // Initialize mobile menu
    initializeMobileMenu();
    
    // Initialize product functionality if on products page
    if (productGrid) {
        displayProducts(products);
        initializeFilters();
    }
    
    // Initialize contact form if on contact page
    initializeContactForm();
}

// Mobile Menu Toggle
function initializeMobileMenu() {
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    if (menuToggle && navLinks) {
        menuToggle.addEventListener('click', function() {
            navLinks.classList.toggle('active');
        });
    }
}

// Product Display Functions
function displayProducts(productsToShow) {
    if (!productGrid) return;
    
    productGrid.innerHTML = '';
    
    productsToShow.forEach(product => {
        const productCard = createProductCard(product);
        productGrid.appendChild(productCard);
    });
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = `product-card ${product.brand}`;
    card.innerHTML = `
        <img src="${product.image}" alt="${product.name}" class="product-image">
        <div class="product-info">
            <h3 class="product-name">${product.name}</h3>
            <p class="product-description">${product.description}</p>
            <div class="product-price">${product.price}</div>
            <a href="${generateWhatsAppLink(product.name)}" class="whatsapp-btn" target="_blank">
                📱 Order on WhatsApp
            </a>
        </div>
    `;
    return card;
}

// WhatsApp Integration
function generateWhatsAppLink(productName) {
    const message = `I would like to order ${productName}`;
    const encodedMessage = encodeURIComponent(message);
    return `https://wa.me/${WHATSAPP_NUMBER}?text=${encodedMessage}`;
}

// Filter and Search Functions
function initializeFilters() {
    if (searchBox) {
        searchBox.addEventListener('input', filterProducts);
    }
    
    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterProducts);
    }
}

function filterProducts() {
    const searchTerm = searchBox ? searchBox.value.toLowerCase() : '';
    const selectedCategory = categoryFilter ? categoryFilter.value : 'all';
    
    let filteredProducts = products;
    
    // Filter by category
    if (selectedCategory !== 'all') {
        filteredProducts = filteredProducts.filter(product => 
            product.category === selectedCategory
        );
    }
    
    // Filter by search term
    if (searchTerm) {
        filteredProducts = filteredProducts.filter(product =>
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.brand.toLowerCase().includes(searchTerm)
        );
    }
    
    displayProducts(filteredProducts);
}

// Contact Form Handling
function initializeContactForm() {
    const contactForm = document.getElementById('contact-form');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(contactForm);
            const name = formData.get('name');
            const email = formData.get('email');
            const subject = formData.get('subject');
            const message = formData.get('message');
            
            // Create WhatsApp message
            const whatsappMessage = `Contact Form Submission:
Name: ${name}
Email: ${email}
Subject: ${subject}
Message: ${message}`;
            
            const encodedMessage = encodeURIComponent(whatsappMessage);
            const whatsappUrl = `https://wa.me/${WHATSAPP_NUMBER}?text=${encodedMessage}`;
            
            // Open WhatsApp
            window.open(whatsappUrl, '_blank');
            
            // Reset form
            contactForm.reset();
            
            // Show success message
            alert('Thank you for your message! We will redirect you to WhatsApp to complete your inquiry.');
        });
    }
}

// Utility Functions
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Add smooth scrolling for anchor links
document.addEventListener('click', function(e) {
    if (e.target.tagName === 'A' && e.target.getAttribute('href').startsWith('#')) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth'
            });
        }
    }
});

// Export functions for global access if needed
window.SailandTech = {
    filterProducts,
    generateWhatsAppLink,
    scrollToTop
};

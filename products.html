<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Sailand Technology LTD</title>
    <meta name="description" content="Browse our complete range of IT products including laptops, desktops, accessories, servers, and CCTV systems from top brands.">
    <meta name="keywords" content="IT products, laptops, desktops, HP, Dell, Lenovo, Logitech, SanDisk, servers, CCTV">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Header and Navigation -->
    <header>
        <nav class="container">
            <div class="logo">Sailand Technology LTD</div>
            <ul class="nav-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="products.html">Products</a></li>
                <li><a href="about.html">About Us</a></li>
                <li><a href="contact.html">Contact</a></li>
            </ul>
            <button class="menu-toggle">☰</button>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="hero" style="padding: 2rem 0;">
        <div class="container">
            <h1>Our Products</h1>
            <p>Discover our comprehensive range of enterprise and consumer IT solutions</p>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <!-- Search and Filter Bar -->
            <div class="filter-bar">
                <input type="text" id="search-box" class="search-box" placeholder="Search products by name, brand, or description...">
                <select id="category-filter" class="category-filter">
                    <option value="all">All Categories</option>
                    <option value="laptops">Laptops</option>
                    <option value="desktops">Desktops</option>
                    <option value="accessories">Accessories</option>
                    <option value="flash-drives">Flash Drives</option>
                    <option value="ram">RAM Modules</option>
                    <option value="monitors">Monitors</option>
                    <option value="servers">Servers</option>
                    <option value="cctv">CCTV Systems</option>
                    <option value="cables">Cables</option>
                </select>
            </div>

            <!-- Product Grid -->
            <div id="product-grid" class="product-grid">
                <!-- Products will be dynamically loaded here by JavaScript -->
            </div>

            <!-- No Results Message -->
            <div id="no-results" class="text-center" style="display: none; padding: 3rem 0;">
                <h3 style="color: #666;">No products found</h3>
                <p style="color: #999;">Try adjusting your search terms or category filter</p>
            </div>
        </div>
    </section>

    <!-- Brand Information Section -->
    <section class="products-section" style="background-color: #f9f9f9;">
        <div class="container">
            <h2 class="section-title">Trusted Brands We Carry</h2>
            
            <div class="product-grid">
                <div class="product-card hp">
                    <div class="product-info">
                        <h3 class="product-name">HP</h3>
                        <p class="product-description">Leading provider of business laptops, desktops, servers, and enterprise solutions with proven reliability</p>
                    </div>
                </div>

                <div class="product-card dell">
                    <div class="product-info">
                        <h3 class="product-name">Dell</h3>
                        <p class="product-description">Innovative technology solutions including OptiPlex desktops, Latitude laptops, and PowerEdge servers</p>
                    </div>
                </div>

                <div class="product-card lenovo">
                    <div class="product-info">
                        <h3 class="product-name">Lenovo</h3>
                        <p class="product-description">ThinkPad and ThinkCentre series known for durability and performance in business environments</p>
                    </div>
                </div>

                <div class="product-card logitech">
                    <div class="product-info">
                        <h3 class="product-name">Logitech</h3>
                        <p class="product-description">Premium mice, keyboards, webcams, and presentation tools for professional productivity</p>
                    </div>
                </div>

                <div class="product-card sandisk">
                    <div class="product-info">
                        <h3 class="product-name">SanDisk</h3>
                        <p class="product-description">Reliable flash storage solutions including USB drives, SD cards, and portable SSDs</p>
                    </div>
                </div>

                <div class="product-card lexar">
                    <div class="product-info">
                        <h3 class="product-name">Lexar</h3>
                        <p class="product-description">High-performance memory solutions including RAM modules and professional storage devices</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Categories Information -->
    <section class="products-section">
        <div class="container">
            <h2 class="section-title">Product Categories</h2>
            
            <div class="page-content">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                    <div>
                        <h3 style="color: #D32F2F; margin-bottom: 1rem;">💻 Laptops & Desktops</h3>
                        <p>Professional business computers from HP, Dell, and Lenovo designed for enterprise environments. Custom configurations available.</p>
                    </div>
                    
                    <div>
                        <h3 style="color: #D32F2F; margin-bottom: 1rem;">🖱️ Accessories</h3>
                        <p>Mice, keyboards, pointers, and other peripherals from leading brands to enhance productivity and comfort.</p>
                    </div>
                    
                    <div>
                        <h3 style="color: #D32F2F; margin-bottom: 1rem;">💾 Storage Solutions</h3>
                        <p>Flash drives, RAM modules, and storage devices for data backup, transfer, and system upgrades.</p>
                    </div>
                    
                    <div>
                        <h3 style="color: #D32F2F; margin-bottom: 1rem;">🖥️ Monitors</h3>
                        <p>Professional displays for business use with various sizes and resolutions to meet different requirements.</p>
                    </div>
                    
                    <div>
                        <h3 style="color: #D32F2F; margin-bottom: 1rem;">🏢 Servers</h3>
                        <p>Enterprise-grade servers and server accessories for data centers and business infrastructure.</p>
                    </div>
                    
                    <div>
                        <h3 style="color: #D32F2F; margin-bottom: 1rem;">📹 CCTV Systems</h3>
                        <p>Complete surveillance and security solutions for business protection with professional installation.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="hero" style="padding: 3rem 0;">
        <div class="container">
            <h2>Need a Custom Quote?</h2>
            <p>Contact us for personalized pricing and bulk order discounts</p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-top: 2rem;">
                <a href="contact.html" class="cta-button">Request Quote</a>
                <a href="https://wa.me/1234567890?text=Hello%20Sailand%20Technology%2C%20I%20need%20a%20quote%20for%20IT%20products" class="whatsapp-btn" target="_blank" style="background-color: #25D366; color: white;">📱 WhatsApp Quote</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Sailand Technology LTD</h3>
                    <p>Your trusted partner for enterprise and consumer IT solutions. We provide quality products from leading brands with professional service.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <p><a href="index.html">Home</a></p>
                    <p><a href="products.html">Products</a></p>
                    <p><a href="about.html">About Us</a></p>
                    <p><a href="contact.html">Contact</a></p>
                </div>
                
                <div class="footer-section">
                    <h3>Product Categories</h3>
                    <p><a href="products.html?category=laptops">Laptops</a></p>
                    <p><a href="products.html?category=desktops">Desktops</a></p>
                    <p><a href="products.html?category=accessories">Accessories</a></p>
                    <p><a href="products.html?category=servers">Servers</a></p>
                </div>
                
                <div class="footer-section">
                    <h3>Contact Info</h3>
                    <p>📧 <EMAIL></p>
                    <p>📞 +1 (234) 567-8900</p>
                    <p>📱 WhatsApp: +1 (234) 567-8900</p>
                    <p>📍 Business Address Here</p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Sailand Technology LTD. All rights reserved. | Professional IT Solutions</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    
    <!-- Handle URL parameters for category filtering -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const category = urlParams.get('category');
            
            if (category && document.getElementById('category-filter')) {
                document.getElementById('category-filter').value = category;
                // Trigger filter function if it exists
                if (typeof filterProducts === 'function') {
                    filterProducts();
                }
            }
        });
    </script>
</body>
</html>

# Images Directory

This directory is for storing product images and other website assets.

## 📁 Recommended Structure

```
images/
├── products/
│   ├── laptops/
│   │   ├── hp-elitebook-840.jpg
│   │   ├── dell-latitude-7420.jpg
│   │   └── lenovo-thinkpad-x1.jpg
│   ├── desktops/
│   │   ├── hp-elitedesk-800.jpg
│   │   └── dell-optiplex-7090.jpg
│   ├── accessories/
│   │   ├── logitech-mx-master-3s.jpg
│   │   └── hp-wireless-keyboard.jpg
│   ├── storage/
│   │   ├── sandisk-ultra-64gb.jpg
│   │   └── lexar-ddr4-16gb.jpg
│   └── servers/
│       └── hp-proliant-dl380.jpg
├── brands/
│   ├── hp-logo.png
│   ├── dell-logo.png
│   ├── lenovo-logo.png
│   ├── logitech-logo.png
│   ├── sandisk-logo.png
│   └── lexar-logo.png
└── company/
    ├── sailand-logo.png
    ├── office-photo.jpg
    └── team-photo.jpg
```

## 🖼️ Image Guidelines

### Product Images
- **Format**: JPG or PNG
- **Size**: 300x200px minimum (maintains aspect ratio)
- **Quality**: High resolution for zoom effects
- **Background**: Clean, preferably white
- **Naming**: Use descriptive, SEO-friendly names

### Brand Logos
- **Format**: PNG with transparency
- **Size**: Various sizes (50x50, 100x100, 200x200)
- **Quality**: Vector-based preferred

### Company Images
- **Format**: JPG for photos, PNG for graphics
- **Size**: Varies by usage
- **Quality**: Professional, high-resolution

## 🔄 Updating Images

To replace placeholder images:

1. **Add images to appropriate folders**
2. **Update image paths in `js/script.js`**:
   ```javascript
   image: "images/products/laptops/hp-elitebook-840.jpg"
   ```
3. **Update category highlight images in `index.html`**
4. **Test all image links**

## 📝 Current Status

Currently using placeholder images from `via.placeholder.com`:
- All product images are placeholders
- Category images are placeholders
- Replace with actual product photos when available

## 🎯 Image Optimization Tips

- Compress images for web (use tools like TinyPNG)
- Use appropriate formats (JPG for photos, PNG for graphics)
- Consider WebP format for better compression
- Add alt text for accessibility
- Use responsive images for different screen sizes
